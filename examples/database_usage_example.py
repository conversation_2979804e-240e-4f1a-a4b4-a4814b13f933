#!/usr/bin/env python3
"""
Example usage of the BotUser and ConversationMessage entities with MongoDB repositories.

This script demonstrates how to:
1. Initialize the database with proper indexes
2. Create and manage bot users
3. Store and retrieve conversation messages
4. Perform common database operations
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import our models and services
from app.utils.models import BotUser, ConversationMessage
from app.db import database_service
from app.utils.database_state_manager import database_client


async def initialize_database():
    """Initialize the database connection and setup."""
    logger.info("Initializing database...")

    # Initialize the database service
    await database_service.initialize()

    # Initialize collections and indexes using database client
    from app.utils.database_events import InitializeDatabaseRequest, InitializeDatabaseResponse
    import uuid

    request = InitializeDatabaseRequest(request_id=str(uuid.uuid4()))
    response = await database_client.state_manager.request_with_response(
        request,
        InitializeDatabaseResponse
    )

    logger.info(f"Database initialization results: {response.initialization_results}")
    return response.initialization_results


async def demonstrate_user_operations():
    """Demonstrate BotUser CRUD operations."""
    logger.info("\n=== User Operations Demo ===")

    # Create a new user
    new_user = BotUser(
        telegram_user_id=123456789,
        username="john_doe",
        first_name="John",
        last_name="Doe",
        language_code="en"
    )

    try:
        # Save the user using database client
        created_user = await database_client.create_user(new_user)
        logger.info(f"Created user: {created_user.get_display_name()}")

        # Retrieve the user by Telegram ID
        retrieved_user = await database_client.get_user_by_telegram_id(123456789)
        if retrieved_user:
            logger.info(f"Retrieved user: {retrieved_user.get_display_name()}")

        # Update user's last seen
        await database_client.update_user_last_seen(123456789)
        logger.info("Updated user's last seen timestamp")

        # Increment message count
        await database_client.increment_user_message_count(123456789)
        logger.info("Incremented user's message count")

        # Get updated user
        updated_user = await database_client.get_user_by_telegram_id(123456789)
        if updated_user:
            logger.info(f"User message count: {updated_user.total_messages}")
            logger.info(f"User last seen: {updated_user.last_seen_at}")

        return created_user

    except Exception as e:
        logger.error(f"Error in user operations: {e}")
        return None


async def demonstrate_message_operations(user: BotUser):
    """Demonstrate ConversationMessage CRUD operations."""
    logger.info("\n=== Message Operations Demo ===")
    
    # Create repository instance
    message_repo = ConversationMessageRepository()
    
    # Create a sample Telegram message
    telegram_message = {
        "message_id": 1001,
        "from": {
            "id": user.telegram_user_id,
            "username": user.username,
            "first_name": user.first_name,
            "last_name": user.last_name
        },
        "text": "Hello, this is a test message!",
        "date": int(datetime.now().timestamp()),
        "chat": {
            "id": -1001234567890,
            "type": "private"
        }
    }
    
    try:
        # Create message from Telegram data
        new_message = ConversationMessage.from_telegram_message(
            telegram_message, 
            chat_id=-1001234567890
        )
        
        # Save the message
        created_message = await message_repo.create_message(new_message)
        logger.info(f"Created message: {created_message.message_text[:50]}...")
        
        # Mark message as processing
        created_message.mark_as_processing()
        await message_repo.update_message(created_message)
        logger.info("Marked message as processing")
        
        # Mark message as processed
        if created_message.id:
            await message_repo.mark_message_as_processed(created_message.id)
        logger.info("Marked message as processed")

        # Create a bot response message
        bot_message = ConversationMessage.create_bot_message(
            chat_id=-1001234567890,
            message_text="Hello! I received your message.",
            reply_to_message_id=created_message.telegram_message_id
        )
        created_bot_message = await message_repo.create_message(bot_message)
        logger.info("Created bot response message")
        
        # Retrieve conversation history
        history = await message_repo.get_conversation_history(
            chat_id=-1001234567890,
            telegram_user_id=user.telegram_user_id,
            limit=10
        )
        logger.info(f"Retrieved {len(history)} messages from conversation history")
        
        # Get recent messages
        recent = await message_repo.get_recent_messages(
            chat_id=-1001234567890,
            hours=24
        )
        logger.info(f"Retrieved {len(recent)} recent messages")
        
        return created_message
        
    except Exception as e:
        logger.error(f"Error in message operations: {e}")
        return None


async def demonstrate_search_operations():
    """Demonstrate search and query operations."""
    logger.info("\n=== Search Operations Demo ===")
    
    user_repo = BotUserRepository()
    message_repo = ConversationMessageRepository()
    
    try:
        # Get active users
        active_users = await user_repo.get_active_users(limit=10)
        logger.info(f"Found {len(active_users)} active users")
        
        # Get unprocessed messages
        unprocessed = await message_repo.get_unprocessed_messages(limit=10)
        logger.info(f"Found {len(unprocessed)} unprocessed messages")
        
    except Exception as e:
        logger.error(f"Error in search operations: {e}")


async def demonstrate_health_check():
    """Demonstrate database health check."""
    logger.info("\n=== Health Check Demo ===")
    
    try:
        connection_manager = get_connection_manager()
        health_status = await connection_manager.health_check()
        logger.info(f"Database health: {health_status}")

    except Exception as e:
        logger.error(f"Error in health check: {e}")


async def cleanup_demo_data():
    """Clean up demo data (optional)."""
    logger.info("\n=== Cleanup Demo Data ===")
    
    user_repo = BotUserRepository()
    
    try:
        # Delete the demo user
        deleted = await user_repo.delete_user(123456789)
        if deleted:
            logger.info("Deleted demo user")
        else:
            logger.info("Demo user not found for deletion")
            
    except Exception as e:
        logger.error(f"Error in cleanup: {e}")


async def main():
    """Main demonstration function."""
    try:
        # Initialize database
        await initialize_database()
        
        # Demonstrate user operations
        demo_user = await demonstrate_user_operations()
        
        if demo_user:
            # Demonstrate message operations
            await demonstrate_message_operations(demo_user)
        
        # Demonstrate search operations
        await demonstrate_search_operations()
        
        # Demonstrate health check
        await demonstrate_health_check()
        
        # Optional: Clean up demo data
        # await cleanup_demo_data()
        
    except Exception as e:
        logger.error(f"Demo failed: {e}")
    
    finally:
        # Disconnect from database
        connection_manager = get_connection_manager()
        await connection_manager.disconnect()
        logger.info("Database connection closed")


if __name__ == "__main__":
    asyncio.run(main())
