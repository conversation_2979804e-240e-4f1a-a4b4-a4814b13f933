import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from app.utils.models import BotUser, ConversationMessage
from app.utils.database_state_manager import database_client

logger = logging.getLogger(__name__)


class UserIdentifier:
    """Identifies users and loads their context and integrations."""

    def __init__(self):
        # Use database client for persistent storage
        self.db_client = database_client
        # Keep a small cache for frequently accessed data
        self.user_cache: Dict[int, Dict[str, Any]] = {}
    
    async def identify_user(self, user_id: int, username: Optional[str] = None,
                          first_name: Optional[str] = None,
                          last_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Identify user and load their context.

        Returns:
            Dict containing user profile, integrations, and preferences
        """
        try:
            # Check cache first
            if user_id in self.user_cache:
                user_data = self.user_cache[user_id]
                logger.debug(f"User {user_id} loaded from cache")

                # Update last seen in database
                await self.db_client.update_user_last_seen(user_id)
                user_data["last_seen"] = datetime.now()

                return user_data

            # Try to get user from database
            bot_user = await self.db_client.get_user_by_telegram_id(user_id)

            if bot_user:
                # User exists in database
                user_data = self._bot_user_to_dict(bot_user)
                self.user_cache[user_id] = user_data
                logger.debug(f"User {user_id} loaded from database")

                # Update last seen
                await self.db_client.update_user_last_seen(user_id)
                user_data["last_seen"] = datetime.now()

                return user_data
            else:
                # Create new user
                new_bot_user = BotUser(
                    telegram_user_id=user_id,
                    username=username,
                    first_name=first_name,
                    last_name=last_name
                )

                created_user = await self.db_client.create_user(new_bot_user)
                user_data = self._bot_user_to_dict(created_user)
                self.user_cache[user_id] = user_data
                logger.info(f"Created new user profile for {user_id}")

                return user_data

        except Exception as e:
            logger.error(f"Error identifying user {user_id}: {e}")
            # Return minimal user data on error
            return {
                "user_id": user_id,
                "username": username,
                "first_name": first_name,
                "last_name": last_name,
                "timezone": "UTC",
                "integrations": {},
                "preferences": {},
                "created_at": datetime.now(),
                "last_seen": datetime.now()
            }

    def _bot_user_to_dict(self, bot_user: BotUser) -> Dict[str, Any]:
        """Convert BotUser to dictionary format."""
        return {
            "user_id": bot_user.telegram_user_id,
            "username": bot_user.username,
            "first_name": bot_user.first_name,
            "last_name": bot_user.last_name,
            "timezone": bot_user.timezone,
            "integrations": {},  # Will be populated from other sources
            "preferences": {},   # Will be populated from other sources
            "created_at": bot_user.created_at,
            "last_seen": bot_user.last_seen_at or datetime.now()
        }

    async def get_conversation_history(self, user_id: int, chat_id: int, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent conversation history for the user."""
        try:
            # Get conversation history from database
            messages = await self.db_client.get_conversation_history(
                chat_id=chat_id,
                telegram_user_id=user_id,
                limit=limit
            )

            # Convert to the expected format
            history = []
            for message in messages:
                history.append({
                    "role": "bot" if message.is_from_bot else "user",
                    "content": message.message_text,
                    "timestamp": message.telegram_timestamp
                })

            logger.debug(f"Retrieved {len(history)} messages for user {user_id}")
            return history

        except Exception as e:
            logger.error(f"Error getting conversation history for user {user_id}: {e}")
            return []
    
    async def add_to_conversation_history(self, user_id: int, chat_id: int, message: str,
                                        role: str = "user") -> None:
        """Add message to conversation history."""
        try:
            # Create ConversationMessage and store in database
            if role == "bot":
                conversation_message = ConversationMessage.create_bot_message(
                    chat_id=chat_id,
                    message_text=message
                )
            else:
                # For user messages, we'll create a basic message
                # In practice, this would come from the Telegram message data
                conversation_message = ConversationMessage(
                    telegram_message_id=0,  # This should be the actual Telegram message ID
                    chat_id=chat_id,
                    telegram_user_id=user_id,
                    message_text=message,
                    telegram_timestamp=datetime.now(),
                    is_from_bot=False
                )

            # Store in database
            await self.db_client.create_message(conversation_message)

            # Also increment user message count if it's a user message
            if role == "user":
                await self.db_client.increment_user_message_count(user_id)

            logger.debug(f"Added {role} message to conversation history for user {user_id}")

        except Exception as e:
            logger.error(f"Error adding to conversation history: {e}")
    
    async def get_user_integrations(self, user_id: int) -> Dict[str, Any]:
        """Get available integrations for the user."""
        try:
            user_data = await self.identify_user(user_id)
            integrations = user_data.get("integrations", {})
            
            # In a real implementation, this would check actual integration status
            # For now, return mock data
            available_integrations = {
                "google_calendar": integrations.get("google_calendar", {
                    "enabled": False,
                    "connected": False,
                    "last_sync": None
                }),
                "outlook_calendar": integrations.get("outlook_calendar", {
                    "enabled": False,
                    "connected": False,
                    "last_sync": None
                })
            }
            
            return available_integrations
            
        except Exception as e:
            logger.error(f"Error getting user integrations: {e}")
            return {}
    
    async def _create_user_profile(self, user_id: int, username: Optional[str], 
                                 first_name: Optional[str], 
                                 last_name: Optional[str]) -> Dict[str, Any]:
        """Create a new user profile."""
        return {
            "user_id": user_id,
            "username": username,
            "first_name": first_name,
            "last_name": last_name,
            "timezone": "UTC",  # Default timezone
            "integrations": {
                "google_calendar": {
                    "enabled": False,
                    "connected": False,
                    "credentials": None,
                    "last_sync": None
                },
                "outlook_calendar": {
                    "enabled": False,
                    "connected": False,
                    "credentials": None,
                    "last_sync": None
                }
            },
            "preferences": {
                "default_event_duration": 60,  # minutes
                "notification_preferences": {
                    "email": False,
                    "push": True
                },
                "working_hours": {
                    "start": "09:00",
                    "end": "17:00",
                    "days": ["monday", "tuesday", "wednesday", "thursday", "friday"]
                }
            },
            "created_at": datetime.now(),
            "last_seen": datetime.now()
        }
