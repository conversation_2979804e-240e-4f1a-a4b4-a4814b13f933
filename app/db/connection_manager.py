"""
Database connection manager for MongoDB Atlas.

This module provides a connection manager that handles MongoDB connections
with proper lifecycle management, connection pooling, and health monitoring.
"""

import logging
from typing import Optional, Dict, Any
from contextlib import asynccontextmanager
from pymongo import MongoClient
from pymongo.database import Database
from pymongo.collection import Collection
from pymongo.server_api import ServerApi
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError

from app.config.settings import settings

logger = logging.getLogger(__name__)


class ConnectionManager:
    """
    MongoDB connection manager with proper lifecycle management.
    
    Provides connection pooling, health monitoring, and database access
    without maintaining a global singleton instance.
    """
    
    def __init__(self):
        self._client: Optional[MongoClient] = None
        self._database: Optional[Database] = None
        self._is_connected = False
    
    async def connect(self) -> None:
        """Establish connection to MongoDB Atlas."""
        if self._is_connected:
            logger.info("Database already connected")
            return
        
        try:
            # Create MongoDB client with configuration from settings
            self._client = MongoClient(
                settings.database.mongodb_url,
                server_api=ServerApi('1'),
                serverSelectionTimeoutMS=5000,  # 5 second timeout
                connectTimeoutMS=10000,  # 10 second connection timeout
                socketTimeoutMS=20000,  # 20 second socket timeout
                maxPoolSize=50,  # Maximum number of connections in the pool
                minPoolSize=5,   # Minimum number of connections in the pool
                retryWrites=True
            )
            
            # Get database instance
            self._database = self._client[settings.database.database_name]
            
            # Test the connection
            await self._test_connection()
            
            self._is_connected = True
            logger.info(f"Successfully connected to MongoDB database: {settings.database.database_name}")
            
        except (ConnectionFailure, ServerSelectionTimeoutError) as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            self._is_connected = False
            raise
        except Exception as e:
            logger.error(f"Unexpected error connecting to MongoDB: {e}")
            self._is_connected = False
            raise
    
    async def disconnect(self) -> None:
        """Close the MongoDB connection."""
        if self._client:
            self._client.close()
            self._client = None
            self._database = None
            self._is_connected = False
            logger.info("Disconnected from MongoDB")
    
    async def _test_connection(self) -> None:
        """Test the MongoDB connection."""
        if not self._client:
            raise ConnectionFailure("No MongoDB client available")
        
        try:
            # Send a ping to confirm connection
            self._client.admin.command('ping')
            logger.info("MongoDB connection test successful")
        except Exception as e:
            logger.error(f"MongoDB connection test failed: {e}")
            raise
    
    def get_database(self) -> Database:
        """Get the MongoDB database instance."""
        if not self._is_connected or self._database is None:
            raise ConnectionFailure("Database not connected. Call connect() first.")
        return self._database
    
    def get_collection(self, collection_name: str) -> Collection:
        """Get a specific collection from the database."""
        database = self.get_database()
        return database[collection_name]
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform a health check on the database connection."""
        try:
            if not self._is_connected:
                return {
                    "status": "error",
                    "message": "Database not connected",
                    "connected": False
                }
            
            # Test connection
            await self._test_connection()
            
            # Get server status
            if self._client is not None:
                server_status = self._client.admin.command("serverStatus")
            else:
                server_status = {}
            
            return {
                "status": "healthy",
                "message": "Database connection is healthy",
                "connected": True,
                "server_version": server_status.get("version"),
                "uptime": server_status.get("uptime"),
                "database_name": settings.database.database_name
            }
            
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return {
                "status": "error",
                "message": f"Database health check failed: {str(e)}",
                "connected": False
            }
    
    @property
    def is_connected(self) -> bool:
        """Check if the database is connected."""
        return self._is_connected
    
    @asynccontextmanager
    async def get_connection(self):
        """Context manager for database connections."""
        if not self._is_connected:
            await self.connect()
        
        try:
            yield self
        finally:
            # Don't automatically disconnect - let the application manage lifecycle
            pass


# Global connection manager instance
_connection_manager: Optional[ConnectionManager] = None


def get_connection_manager() -> ConnectionManager:
    """
    Get the global connection manager instance.
    
    This provides a better pattern than a singleton by allowing
    dependency injection while still providing a convenient global access.
    """
    global _connection_manager
    if _connection_manager is None:
        _connection_manager = ConnectionManager()
    return _connection_manager


async def initialize_database() -> ConnectionManager:
    """
    Initialize the database connection.
    
    Returns:
        ConnectionManager: The initialized connection manager
    """
    manager = get_connection_manager()
    await manager.connect()
    return manager


async def cleanup_database() -> None:
    """Clean up database connections."""
    global _connection_manager
    if _connection_manager:
        await _connection_manager.disconnect()
        _connection_manager = None
