"""
Database package for MongoDB operations with event bus integration.

This package provides database connection management, repositories,
and event-driven database operations for MongoDB Atlas integration.
"""

from .connection_manager import ConnectionManager, get_connection_manager
from .database_initializer import DatabaseInitializer
from .bot_user_repository import BotUserRepository
from .conversation_message_repository import ConversationMessageRepository
from .database_service import DatabaseService, database_service

__all__ = [
    "ConnectionManager",
    "get_connection_manager",
    "DatabaseInitializer",
    "BotUserRepository",
    "ConversationMessageRepository",
    "DatabaseService",
    "database_service"
]
