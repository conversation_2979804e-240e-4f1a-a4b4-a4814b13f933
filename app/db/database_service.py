"""
Database service that handles database operations via event bus.

This service subscribes to database events and performs CRUD operations,
publishing results back through the event bus for decoupled communication.
"""

import logging
import uuid
from datetime import datetime
from typing import Dict, Any

from app.utils.event_bus import event_bus
from app.utils.database_events import *
from .connection_manager import get_connection_manager
from .database_initializer import DatabaseInitializer
from .bot_user_repository import BotUserRepository
from .conversation_message_repository import ConversationMessageRepository

logger = logging.getLogger(__name__)


class DatabaseService:
    """
    Event-driven database service that handles all database operations.
    
    This service subscribes to database events and performs operations
    through repositories, maintaining proper isolation and decoupling.
    """
    
    def __init__(self):
        self.connection_manager = get_connection_manager()
        self.database_initializer = DatabaseInitializer()
        self.user_repository = BotUserRepository()
        self.message_repository = ConversationMessageRepository()
        
        # Subscribe to all database events
        event_bus.subscribe_tagged_methods(self)
        
        logger.info("DatabaseService initialized and subscribed to events")
    
    async def initialize(self) -> None:
        """Initialize the database connection."""
        await self.connection_manager.connect()
        logger.info("Database service initialized")
    
    async def cleanup(self) -> None:
        """Clean up database connections."""
        await self.connection_manager.disconnect()
        logger.info("Database service cleaned up")
    
    # BotUser operations
    @event_bus.subscribe("CreateUserRequest")
    async def handle_create_user(self, event: CreateUserRequest) -> None:
        """Handle user creation request."""
        try:
            start_time = datetime.now()
            
            # Publish operation started event
            await event_bus.publish(DatabaseOperationStarted(
                request_id=event.request_id,
                operation_type="create_user"
            ))
            
            # Perform the operation
            created_user = await self.user_repository.create_user(event.user)
            
            # Calculate duration
            duration = (datetime.now() - start_time).total_seconds()
            
            # Publish success response
            await event_bus.publish(CreateUserResponse(
                request_id=event.request_id,
                success=True,
                user=created_user
            ))
            
            # Publish operation completed event
            await event_bus.publish(DatabaseOperationCompleted(
                request_id=event.request_id,
                operation_type="create_user",
                success=True,
                duration=duration
            ))
            
        except Exception as e:
            logger.error(f"Error creating user: {e}")
            
            # Publish error response
            await event_bus.publish(CreateUserResponse(
                request_id=event.request_id,
                success=False,
                error_message=str(e)
            ))
            
            # Publish operation failed event
            await event_bus.publish(DatabaseOperationFailed(
                request_id=event.request_id,
                operation_type="create_user",
                error_message=str(e)
            ))
    
    @event_bus.subscribe("GetUserByTelegramIdRequest")
    async def handle_get_user_by_telegram_id(self, event: GetUserByTelegramIdRequest) -> None:
        """Handle get user by Telegram ID request."""
        try:
            start_time = datetime.now()
            
            await event_bus.publish(DatabaseOperationStarted(
                request_id=event.request_id,
                operation_type="get_user_by_telegram_id"
            ))
            
            user = await self.user_repository.get_user_by_telegram_id(event.telegram_user_id)
            
            duration = (datetime.now() - start_time).total_seconds()
            
            await event_bus.publish(GetUserByTelegramIdResponse(
                request_id=event.request_id,
                success=True,
                user=user
            ))
            
            await event_bus.publish(DatabaseOperationCompleted(
                request_id=event.request_id,
                operation_type="get_user_by_telegram_id",
                success=True,
                duration=duration
            ))
            
        except Exception as e:
            logger.error(f"Error getting user by telegram ID: {e}")
            
            await event_bus.publish(GetUserByTelegramIdResponse(
                request_id=event.request_id,
                success=False,
                error_message=str(e)
            ))
            
            await event_bus.publish(DatabaseOperationFailed(
                request_id=event.request_id,
                operation_type="get_user_by_telegram_id",
                error_message=str(e)
            ))
    
    @event_bus.subscribe("UpdateUserLastSeenRequest")
    async def handle_update_user_last_seen(self, event: UpdateUserLastSeenRequest) -> None:
        """Handle update user last seen request."""
        try:
            start_time = datetime.now()
            
            await event_bus.publish(DatabaseOperationStarted(
                request_id=event.request_id,
                operation_type="update_user_last_seen"
            ))
            
            updated = await self.user_repository.update_last_seen(event.telegram_user_id)
            
            duration = (datetime.now() - start_time).total_seconds()
            
            await event_bus.publish(UpdateUserLastSeenResponse(
                request_id=event.request_id,
                success=True,
                updated=updated
            ))
            
            await event_bus.publish(DatabaseOperationCompleted(
                request_id=event.request_id,
                operation_type="update_user_last_seen",
                success=True,
                duration=duration
            ))
            
        except Exception as e:
            logger.error(f"Error updating user last seen: {e}")
            
            await event_bus.publish(UpdateUserLastSeenResponse(
                request_id=event.request_id,
                success=False,
                error_message=str(e)
            ))
            
            await event_bus.publish(DatabaseOperationFailed(
                request_id=event.request_id,
                operation_type="update_user_last_seen",
                error_message=str(e)
            ))
    
    @event_bus.subscribe("IncrementUserMessageCountRequest")
    async def handle_increment_user_message_count(self, event: IncrementUserMessageCountRequest) -> None:
        """Handle increment user message count request."""
        try:
            start_time = datetime.now()
            
            await event_bus.publish(DatabaseOperationStarted(
                request_id=event.request_id,
                operation_type="increment_user_message_count"
            ))
            
            updated = await self.user_repository.increment_message_count(event.telegram_user_id)
            
            duration = (datetime.now() - start_time).total_seconds()
            
            await event_bus.publish(IncrementUserMessageCountResponse(
                request_id=event.request_id,
                success=True,
                updated=updated
            ))
            
            await event_bus.publish(DatabaseOperationCompleted(
                request_id=event.request_id,
                operation_type="increment_user_message_count",
                success=True,
                duration=duration
            ))
            
        except Exception as e:
            logger.error(f"Error incrementing user message count: {e}")
            
            await event_bus.publish(IncrementUserMessageCountResponse(
                request_id=event.request_id,
                success=False,
                error_message=str(e)
            ))
            
            await event_bus.publish(DatabaseOperationFailed(
                request_id=event.request_id,
                operation_type="increment_user_message_count",
                error_message=str(e)
            ))
    
    # ConversationMessage operations
    @event_bus.subscribe("CreateMessageRequest")
    async def handle_create_message(self, event: CreateMessageRequest) -> None:
        """Handle message creation request."""
        try:
            start_time = datetime.now()
            
            await event_bus.publish(DatabaseOperationStarted(
                request_id=event.request_id,
                operation_type="create_message"
            ))
            
            created_message = await self.message_repository.create_message(event.message)
            
            duration = (datetime.now() - start_time).total_seconds()
            
            await event_bus.publish(CreateMessageResponse(
                request_id=event.request_id,
                success=True,
                message=created_message
            ))
            
            await event_bus.publish(DatabaseOperationCompleted(
                request_id=event.request_id,
                operation_type="create_message",
                success=True,
                duration=duration
            ))
            
        except Exception as e:
            logger.error(f"Error creating message: {e}")
            
            await event_bus.publish(CreateMessageResponse(
                request_id=event.request_id,
                success=False,
                error_message=str(e)
            ))
            
            await event_bus.publish(DatabaseOperationFailed(
                request_id=event.request_id,
                operation_type="create_message",
                error_message=str(e)
            ))
    
    @event_bus.subscribe("GetConversationHistoryRequest")
    async def handle_get_conversation_history(self, event: GetConversationHistoryRequest) -> None:
        """Handle get conversation history request."""
        try:
            start_time = datetime.now()
            
            await event_bus.publish(DatabaseOperationStarted(
                request_id=event.request_id,
                operation_type="get_conversation_history"
            ))
            
            messages = await self.message_repository.get_conversation_history(
                event.chat_id, 
                event.telegram_user_id, 
                event.limit, 
                event.offset
            )
            
            duration = (datetime.now() - start_time).total_seconds()
            
            await event_bus.publish(GetConversationHistoryResponse(
                request_id=event.request_id,
                success=True,
                messages=messages
            ))
            
            await event_bus.publish(DatabaseOperationCompleted(
                request_id=event.request_id,
                operation_type="get_conversation_history",
                success=True,
                duration=duration
            ))
            
        except Exception as e:
            logger.error(f"Error getting conversation history: {e}")
            
            await event_bus.publish(GetConversationHistoryResponse(
                request_id=event.request_id,
                success=False,
                error_message=str(e)
            ))
            
            await event_bus.publish(DatabaseOperationFailed(
                request_id=event.request_id,
                operation_type="get_conversation_history",
                error_message=str(e)
            ))
    
    # Database management operations
    @event_bus.subscribe("InitializeDatabaseRequest")
    async def handle_initialize_database(self, event: InitializeDatabaseRequest) -> None:
        """Handle database initialization request."""
        try:
            start_time = datetime.now()
            
            await event_bus.publish(DatabaseOperationStarted(
                request_id=event.request_id,
                operation_type="initialize_database"
            ))
            
            results = await self.database_initializer.initialize_database()
            
            duration = (datetime.now() - start_time).total_seconds()
            
            await event_bus.publish(InitializeDatabaseResponse(
                request_id=event.request_id,
                success=True,
                initialization_results=results
            ))
            
            await event_bus.publish(DatabaseOperationCompleted(
                request_id=event.request_id,
                operation_type="initialize_database",
                success=True,
                duration=duration
            ))
            
        except Exception as e:
            logger.error(f"Error initializing database: {e}")
            
            await event_bus.publish(InitializeDatabaseResponse(
                request_id=event.request_id,
                success=False,
                error_message=str(e)
            ))
            
            await event_bus.publish(DatabaseOperationFailed(
                request_id=event.request_id,
                operation_type="initialize_database",
                error_message=str(e)
            ))
    
    @event_bus.subscribe("DatabaseHealthCheckRequest")
    async def handle_database_health_check(self, event: DatabaseHealthCheckRequest) -> None:
        """Handle database health check request."""
        try:
            start_time = datetime.now()
            
            await event_bus.publish(DatabaseOperationStarted(
                request_id=event.request_id,
                operation_type="database_health_check"
            ))
            
            health_status = await self.connection_manager.health_check()
            
            duration = (datetime.now() - start_time).total_seconds()
            
            await event_bus.publish(DatabaseHealthCheckResponse(
                request_id=event.request_id,
                success=True,
                health_status=health_status
            ))
            
            await event_bus.publish(DatabaseOperationCompleted(
                request_id=event.request_id,
                operation_type="database_health_check",
                success=True,
                duration=duration
            ))
            
        except Exception as e:
            logger.error(f"Error performing health check: {e}")
            
            await event_bus.publish(DatabaseHealthCheckResponse(
                request_id=event.request_id,
                success=False,
                error_message=str(e)
            ))
            
            await event_bus.publish(DatabaseOperationFailed(
                request_id=event.request_id,
                operation_type="database_health_check",
                error_message=str(e)
            ))


# Global database service instance
database_service = DatabaseService()
