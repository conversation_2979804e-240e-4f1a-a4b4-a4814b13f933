"""
ConversationMessage entity model for MongoDB storage.

This module defines the ConversationMessage class that represents a message
in a conversation with all necessary information for linking users to conversations
and uniquely identifying messages.
"""

from datetime import datetime
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
from bson import ObjectId


class ConversationMessage(BaseModel):
    """
    ConversationMessage entity representing a message in a conversation.
    
    This class contains information that links users to conversations and
    uniquely identifies messages with content, timing, and chat information.
    """
    
    # MongoDB document ID
    id: Optional[ObjectId] = Field(default=None, alias="_id")
    
    # Message identification
    telegram_message_id: int = Field(..., description="Unique Telegram message ID")
    chat_id: int = Field(..., description="Telegram chat ID where message was sent")
    
    # User identification
    telegram_user_id: int = Field(..., description="Telegram user ID who sent the message")
    
    # Message content
    message_text: str = Field(..., description="The actual message content")
    message_type: str = Field(default="text", description="Type of message (text, photo, document, etc.)")
    
    # Message metadata from Telegram
    username: Optional[str] = Field(default=None, description="Username of the sender")
    first_name: Optional[str] = Field(default=None, description="First name of the sender")
    last_name: Optional[str] = Field(default=None, description="Last name of the sender")
    
    # Timing information
    telegram_timestamp: datetime = Field(..., description="Original timestamp from Telegram")
    received_at: datetime = Field(default_factory=datetime.utcnow, description="When message was received by bot")
    processed_at: Optional[datetime] = Field(default=None, description="When message was processed")
    
    # Conversation context
    is_reply: bool = Field(default=False, description="Whether this message is a reply")
    reply_to_message_id: Optional[int] = Field(default=None, description="ID of message being replied to")
    thread_id: Optional[str] = Field(default=None, description="Conversation thread identifier")
    
    # Message processing status
    processing_status: str = Field(default="received", description="Processing status (received, processing, processed, error)")
    
    # Bot response information
    bot_response: Optional[str] = Field(default=None, description="Bot's response to this message")
    bot_response_timestamp: Optional[datetime] = Field(default=None, description="When bot responded")
    response_processing_time: Optional[float] = Field(default=None, description="Time taken to process and respond")
    
    # LLM and tool information
    llm_provider_used: Optional[str] = Field(default=None, description="LLM provider used for response")
    tokens_used: Optional[int] = Field(default=None, description="Number of tokens used")
    tools_executed: List[str] = Field(default_factory=list, description="List of tools executed")
    
    # Chat context
    chat_type: str = Field(default="private", description="Type of chat (private, group, supergroup, channel)")
    chat_title: Optional[str] = Field(default=None, description="Title of the chat (for groups)")
    
    # Additional metadata
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional message metadata")
    
    # Error tracking
    error_message: Optional[str] = Field(default=None, description="Error message if processing failed")
    retry_count: int = Field(default=0, description="Number of processing retries")
    
    class Config:
        """Pydantic configuration."""
        populate_by_name = True
        arbitrary_types_allowed = True
        json_encoders = {
            ObjectId: str,
            datetime: lambda v: v.isoformat()
        }
    
    def mark_as_processing(self) -> None:
        """Mark the message as being processed."""
        self.processing_status = "processing"
        self.processed_at = datetime.utcnow()
    
    def mark_as_processed(self, bot_response: str, llm_provider: Optional[str] = None,
                         tokens_used: Optional[int] = None, tools_executed: Optional[List[str]] = None,
                         processing_time: Optional[float] = None) -> None:
        """Mark the message as processed with response information."""
        self.processing_status = "processed"
        self.bot_response = bot_response
        self.bot_response_timestamp = datetime.utcnow()
        self.response_processing_time = processing_time
        
        if llm_provider:
            self.llm_provider_used = llm_provider
        if tokens_used:
            self.tokens_used = tokens_used
        if tools_executed:
            self.tools_executed = tools_executed
    
    def mark_as_error(self, error_message: str) -> None:
        """Mark the message as having an error during processing."""
        self.processing_status = "error"
        self.error_message = error_message
        self.retry_count += 1
    
    def set_reply_context(self, reply_to_message_id: int, thread_id: Optional[str] = None) -> None:
        """Set reply context for this message."""
        self.is_reply = True
        self.reply_to_message_id = reply_to_message_id
        if thread_id:
            self.thread_id = thread_id
    
    def update_chat_info(self, chat_type: str, chat_title: Optional[str] = None) -> None:
        """Update chat information."""
        self.chat_type = chat_type
        if chat_title:
            self.chat_title = chat_title
    
    def get_conversation_key(self) -> str:
        """Get a unique key for the conversation this message belongs to."""
        return f"{self.chat_id}_{self.telegram_user_id}"
    
    def get_sender_display_name(self) -> str:
        """Get a display name for the message sender."""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        elif self.first_name:
            return self.first_name
        elif self.username:
            return f"@{self.username}"
        else:
            return f"User {self.telegram_user_id}"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the model to a dictionary for MongoDB storage."""
        data = self.model_dump(by_alias=True, exclude_none=True)
        if self.id:
            data["_id"] = self.id
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ConversationMessage":
        """Create a ConversationMessage instance from a dictionary."""
        if "_id" in data:
            data["id"] = data["_id"]
        return cls(**data)
    
    @classmethod
    def from_telegram_message(cls, telegram_message: Dict[str, Any], chat_id: int) -> "ConversationMessage":
        """Create a ConversationMessage from a Telegram message object."""
        user = telegram_message.get("from", {})
        
        return cls(
            telegram_message_id=telegram_message["message_id"],
            chat_id=chat_id,
            telegram_user_id=user.get("id"),
            message_text=telegram_message.get("text", ""),
            username=user.get("username"),
            first_name=user.get("first_name"),
            last_name=user.get("last_name"),
            telegram_timestamp=datetime.fromtimestamp(telegram_message["date"]),
            chat_type=telegram_message.get("chat", {}).get("type", "private"),
            chat_title=telegram_message.get("chat", {}).get("title"),
            is_reply=bool(telegram_message.get("reply_to_message")),
            reply_to_message_id=telegram_message.get("reply_to_message", {}).get("message_id")
        )
