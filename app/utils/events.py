from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from app.utils.event_bus import BaseEvent

# Import database events
from app.utils.database_events import *


@dataclass
class MessageReceived(BaseEvent):
    """Event published when a message is received from Telegram."""
    user_id: int
    chat_id: int
    message_id: int
    message_text: str
    username: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    timestamp: Optional[datetime] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class MessagePreprocessed(BaseEvent):
    """Event published when message preprocessing is complete."""
    user_id: int
    chat_id: int
    message_id: int
    original_message: str
    processed_prompt: str
    user_context: Dict[str, Any]
    conversation_history: List[Dict[str, Any]]
    available_tools: List[str]
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class LLMResponseReceived(BaseEvent):
    """Event published when LLM response is received."""
    user_id: int
    chat_id: int
    message_id: int
    response_text: str
    tool_calls: List[Dict[str, Any]]
    provider_used: str
    tokens_used: int
    processing_time: float
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class ToolExecutionStarted(BaseEvent):
    """Event published when tool execution begins."""
    user_id: int
    chat_id: int
    message_id: int
    tool_name: str
    tool_parameters: Dict[str, Any]
    execution_id: str
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class ToolExecutionCompleted(BaseEvent):
    """Event published when tool execution completes."""
    user_id: int
    chat_id: int
    message_id: int
    tool_name: str
    execution_id: str
    result: Any
    success: bool
    error_message: Optional[str] = None
    execution_time: float = 0.0
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class ResponseReady(BaseEvent):
    """Event published when final response is ready to send."""
    user_id: int
    chat_id: int
    message_id: int
    response_text: str
    response_type: str = "text"  # text, markdown, html
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class ErrorOccurred(BaseEvent):
    """Event published when an error occurs in the system."""
    user_id: Optional[int]
    chat_id: Optional[int]
    message_id: Optional[int]
    error_type: str
    error_message: str
    component: str
    stack_trace: Optional[str] = None
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class UserSessionStarted(BaseEvent):
    """Event published when a user session starts."""
    user_id: int
    chat_id: int
    username: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class UserSessionEnded(BaseEvent):
    """Event published when a user session ends."""
    user_id: int
    chat_id: int
    session_duration: float
    messages_count: int
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class SystemHealthCheck(BaseEvent):
    """Event published for system health monitoring."""
    component: str
    status: str  # healthy, warning, error
    metrics: Dict[str, Any]
    message: Optional[str] = None
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
