
from pymongo.mongo_client import MongoClient
from pymongo.server_api import ServerApi

uri = "mongodb+srv://jridimed:<EMAIL>/?retryWrites=true&w=majority&appName=FreeBot"
uribot = "mongodb+srv://botcontextgrabber:<EMAIL>/?retryWrites=true&w=majority&appName=FreeBot"
uriworkder = "mongodb+srv://dbworker:<EMAIL>/?retryWrites=true&w=majority&appName=FreeBot"

# Create a new client and connect to the server
client = MongoClient(uri, server_api=ServerApi('1'))

# Send a ping to confirm a successful connection
try:
    client.admin.command('ping')
    print("Pinged your deployment. You successfully connected to MongoDB!")
except Exception as e:
    print(e)