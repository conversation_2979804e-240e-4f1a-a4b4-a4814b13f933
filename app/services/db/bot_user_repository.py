"""
BotUser repository for MongoDB operations.

This module provides CRUD operations for BotUser entities in MongoDB Atlas.
"""

import logging
from typing import Optional, List, Dict, Any
from datetime import datetime, timezone
from pymongo.collection import Collection
from pymongo.errors import Duplicate<PERSON>eyError, PyMongoError
from bson import ObjectId

from app.utils.models.bot_user import BotUser
from .connection_manager import get_connection_manager

logger = logging.getLogger(__name__)


class BotUserRepository:
    """
    Repository class for BotUser CRUD operations in MongoDB.
    
    Provides methods for creating, reading, updating, and deleting
    BotUser entities with proper error handling and logging.
    """
    
    def __init__(self):
        self.collection_name = "bot_users"
    
    def _get_collection(self) -> Collection:
        """Get the bot_users collection from the database."""
        connection_manager = get_connection_manager()
        return connection_manager.get_collection(self.collection_name)
    
    async def create_user(self, user: BotUser) -> BotUser:
        """
        Create a new bot user in the database.
        
        Args:
            user: BotUser instance to create
            
        Returns:
            BotUser: The created user with assigned ID
            
        Raises:
            DuplicateKeyError: If user with same telegram_user_id already exists
            PyMongoError: For other database errors
        """
        try:
            collection = self._get_collection()
            
            # Convert to dict for MongoDB storage
            user_data = user.to_dict()
            
            # Remove _id if it exists (let MongoDB generate it)
            if "_id" in user_data:
                del user_data["_id"]
            
            # Insert the user
            result = collection.insert_one(user_data)
            
            # Update the user object with the new ID
            user.id = result.inserted_id
            
            logger.info(f"Created new bot user: {user.telegram_user_id}")
            return user
            
        except DuplicateKeyError as e:
            logger.error(f"User with telegram_user_id {user.telegram_user_id} already exists")
            raise
        except PyMongoError as e:
            logger.error(f"Database error creating user: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error creating user: {e}")
            raise
    
    async def get_user_by_telegram_id(self, telegram_user_id: int) -> Optional[BotUser]:
        """
        Get a bot user by their Telegram user ID.
        
        Args:
            telegram_user_id: The Telegram user ID
            
        Returns:
            BotUser or None if not found
        """
        try:
            collection = self._get_collection()
            
            user_data = collection.find_one({"telegram_user_id": telegram_user_id})
            
            if user_data:
                return BotUser.from_dict(user_data)
            
            return None
            
        except PyMongoError as e:
            logger.error(f"Database error getting user by telegram_id {telegram_user_id}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error getting user by telegram_id {telegram_user_id}: {e}")
            raise
    
    async def get_user_by_id(self, user_id: ObjectId) -> Optional[BotUser]:
        """
        Get a bot user by their MongoDB ObjectId.
        
        Args:
            user_id: The MongoDB ObjectId
            
        Returns:
            BotUser or None if not found
        """
        try:
            collection = self._get_collection()
            
            user_data = collection.find_one({"_id": user_id})
            
            if user_data:
                return BotUser.from_dict(user_data)
            
            return None
            
        except PyMongoError as e:
            logger.error(f"Database error getting user by id {user_id}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error getting user by id {user_id}: {e}")
            raise
    
    async def update_user(self, user: BotUser) -> BotUser:
        """
        Update an existing bot user in the database.
        
        Args:
            user: BotUser instance with updated data
            
        Returns:
            BotUser: The updated user
            
        Raises:
            ValueError: If user has no ID
            PyMongoError: For database errors
        """
        try:
            if not user.id:
                raise ValueError("User must have an ID to be updated")
            
            collection = self._get_collection()
            
            # Update the updated_at timestamp
            user.updated_at = datetime.now(timezone.utc)
            
            # Convert to dict and remove _id for update
            user_data = user.to_dict()
            if "_id" in user_data:
                del user_data["_id"]
            
            # Update the user
            result = collection.update_one(
                {"_id": user.id},
                {"$set": user_data}
            )
            
            if result.matched_count == 0:
                raise ValueError(f"User with ID {user.id} not found")
            
            logger.info(f"Updated bot user: {user.telegram_user_id}")
            return user
            
        except PyMongoError as e:
            logger.error(f"Database error updating user: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error updating user: {e}")
            raise
    
    async def delete_user(self, telegram_user_id: int) -> bool:
        """
        Delete a bot user by their Telegram user ID.
        
        Args:
            telegram_user_id: The Telegram user ID
            
        Returns:
            bool: True if user was deleted, False if not found
        """
        try:
            collection = self._get_collection()
            
            result = collection.delete_one({"telegram_user_id": telegram_user_id})
            
            if result.deleted_count > 0:
                logger.info(f"Deleted bot user: {telegram_user_id}")
                return True
            else:
                logger.warning(f"User with telegram_user_id {telegram_user_id} not found for deletion")
                return False
                
        except PyMongoError as e:
            logger.error(f"Database error deleting user {telegram_user_id}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error deleting user {telegram_user_id}: {e}")
            raise
    
    async def get_active_users(self, limit: int = 100) -> List[BotUser]:
        """
        Get a list of active bot users.
        
        Args:
            limit: Maximum number of users to return
            
        Returns:
            List[BotUser]: List of active users
        """
        try:
            collection = self._get_collection()
            
            cursor = collection.find({"is_active": True}).limit(limit)
            users = []
            
            for user_data in cursor:
                users.append(BotUser.from_dict(user_data))
            
            return users
            
        except PyMongoError as e:
            logger.error(f"Database error getting active users: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error getting active users: {e}")
            raise
    
    async def update_last_seen(self, telegram_user_id: int) -> bool:
        """
        Update the last seen timestamp for a user.
        
        Args:
            telegram_user_id: The Telegram user ID
            
        Returns:
            bool: True if user was updated, False if not found
        """
        try:
            collection = self._get_collection()
            
            result = collection.update_one(
                {"telegram_user_id": telegram_user_id},
                {
                    "$set": {
                        "last_seen_at": datetime.now(timezone.utc),
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
            
            return result.matched_count > 0
            
        except PyMongoError as e:
            logger.error(f"Database error updating last seen for user {telegram_user_id}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error updating last seen for user {telegram_user_id}: {e}")
            raise
    
    async def increment_message_count(self, telegram_user_id: int) -> bool:
        """
        Increment the message count for a user.
        
        Args:
            telegram_user_id: The Telegram user ID
            
        Returns:
            bool: True if user was updated, False if not found
        """
        try:
            collection = self._get_collection()
            
            result = collection.update_one(
                {"telegram_user_id": telegram_user_id},
                {
                    "$inc": {"total_messages": 1},
                    "$set": {"updated_at": datetime.now(timezone.utc)}
                }
            )
            
            return result.matched_count > 0
            
        except PyMongoError as e:
            logger.error(f"Database error incrementing message count for user {telegram_user_id}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error incrementing message count for user {telegram_user_id}: {e}")
            raise
