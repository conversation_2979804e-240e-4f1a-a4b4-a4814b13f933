"""
Database initialization service for MongoDB Atlas.

This module provides database initialization logic including
collection creation, index setup, and data validation.
"""

import logging
from typing import Dict, Any, List
from pymongo import IndexModel, ASCENDING, DESCENDING, TEXT
from pymongo.errors import PyMongoError

from .connection_manager import get_connection_manager

logger = logging.getLogger(__name__)


class DatabaseInitializer:
    """
    Database initialization service for setting up collections and indexes.
    
    Provides methods for creating collections, setting up indexes,
    and ensuring database schema consistency.
    """
    
    def __init__(self):
        self.collections_config = {
            "bot_users": {
                "indexes": [
                    # Unique index on telegram_user_id for fast user lookups
                    IndexModel([("telegram_user_id", ASCENDING)], unique=True, name="telegram_user_id_unique"),
                    
                    # Index on username for user searches
                    IndexModel([("username", ASCENDING)], name="username_idx"),
                    
                    # Index on is_active for filtering active users
                    IndexModel([("is_active", ASCENDING)], name="is_active_idx"),
                    
                    # Index on last_seen_at for activity tracking
                    IndexModel([("last_seen_at", DESCENDING)], name="last_seen_at_idx"),
                    
                    # Index on created_at for user registration analytics
                    IndexModel([("created_at", DESCENDING)], name="created_at_idx"),
                    
                    # Compound index for active users sorted by last seen
                    IndexModel([
                        ("is_active", ASCENDING),
                        ("last_seen_at", DESCENDING)
                    ], name="active_users_last_seen_idx"),
                    
                    # Text index for searching users by name
                    IndexModel([
                        ("first_name", TEXT),
                        ("last_name", TEXT),
                        ("username", TEXT)
                    ], name="user_search_text_idx")
                ]
            },
            
            "conversation_messages": {
                "indexes": [
                    # Unique compound index on telegram_message_id and chat_id
                    IndexModel([
                        ("telegram_message_id", ASCENDING),
                        ("chat_id", ASCENDING)
                    ], unique=True, name="telegram_message_chat_unique"),
                    
                    # Index on chat_id for chat-specific queries
                    IndexModel([("chat_id", ASCENDING)], name="chat_id_idx"),
                    
                    # Index on telegram_user_id for user-specific queries
                    IndexModel([("telegram_user_id", ASCENDING)], name="telegram_user_id_idx"),
                    
                    # Index on telegram_timestamp for chronological queries
                    IndexModel([("telegram_timestamp", DESCENDING)], name="telegram_timestamp_idx"),
                    
                    # Index on processing_status for finding unprocessed messages
                    IndexModel([("processing_status", ASCENDING)], name="processing_status_idx"),
                    
                    # Index on received_at for processing queue
                    IndexModel([("received_at", ASCENDING)], name="received_at_idx"),
                    
                    # Compound index for conversation history queries
                    IndexModel([
                        ("chat_id", ASCENDING),
                        ("telegram_user_id", ASCENDING),
                        ("telegram_timestamp", DESCENDING)
                    ], name="conversation_history_idx"),
                    
                    # Compound index for recent messages in chat
                    IndexModel([
                        ("chat_id", ASCENDING),
                        ("telegram_timestamp", DESCENDING)
                    ], name="recent_messages_idx"),
                    
                    # Compound index for unprocessed messages queue
                    IndexModel([
                        ("processing_status", ASCENDING),
                        ("received_at", ASCENDING)
                    ], name="processing_queue_idx"),
                    
                    # Index for cleanup operations (old messages)
                    IndexModel([("telegram_timestamp", ASCENDING)], name="cleanup_timestamp_idx"),
                    
                    # Text index for message content search
                    IndexModel([("message_text", TEXT)], name="message_content_search_idx"),
                    
                    # Index on thread_id for conversation threading
                    IndexModel([("thread_id", ASCENDING)], name="thread_id_idx"),
                    
                    # Index on reply_to_message_id for reply chains
                    IndexModel([("reply_to_message_id", ASCENDING)], name="reply_to_message_idx")
                ]
            }
        }
    
    async def initialize_database(self) -> Dict[str, Any]:
        """
        Initialize the database with collections and indexes.
        
        Returns:
            Dict[str, Any]: Initialization results and status
        """
        try:
            logger.info("Starting database initialization...")
            
            results = {
                "status": "success",
                "collections_created": [],
                "indexes_created": {},
                "errors": []
            }
            
            # Ensure database connection
            if not db_service.is_connected:
                await db_service.connect()
            
            database = db_service.get_database()
            
            # Create collections and indexes
            for collection_name, config in self.collections_config.items():
                try:
                    # Create collection if it doesn't exist
                    if collection_name not in database.list_collection_names():
                        database.create_collection(collection_name)
                        results["collections_created"].append(collection_name)
                        logger.info(f"Created collection: {collection_name}")
                    
                    # Create indexes
                    collection = database[collection_name]
                    indexes_created = await self._create_indexes(collection, config["indexes"])
                    results["indexes_created"][collection_name] = indexes_created
                    
                except Exception as e:
                    error_msg = f"Error initializing collection {collection_name}: {str(e)}"
                    logger.error(error_msg)
                    results["errors"].append(error_msg)
            
            # Validate database setup
            validation_results = await self._validate_database_setup()
            results["validation"] = validation_results
            
            if results["errors"]:
                results["status"] = "partial_success"
            
            logger.info("Database initialization completed")
            return results
            
        except Exception as e:
            logger.error(f"Database initialization failed: {e}")
            return {
                "status": "error",
                "message": str(e),
                "collections_created": [],
                "indexes_created": {},
                "errors": [str(e)]
            }
    
    async def _create_indexes(self, collection, index_models: List[IndexModel]) -> List[str]:
        """
        Create indexes for a collection.
        
        Args:
            collection: MongoDB collection
            index_models: List of IndexModel objects
            
        Returns:
            List[str]: Names of created indexes
        """
        try:
            # Get existing indexes
            existing_indexes = set(collection.list_indexes())
            existing_index_names = {idx.get("name") for idx in existing_indexes}
            
            # Filter out indexes that already exist
            new_indexes = []
            for index_model in index_models:
                index_name = index_model.document.get("name")
                if index_name not in existing_index_names:
                    new_indexes.append(index_model)
            
            if new_indexes:
                # Create new indexes
                created_names = collection.create_indexes(new_indexes)
                logger.info(f"Created {len(created_names)} indexes for collection {collection.name}")
                return created_names
            else:
                logger.info(f"All indexes already exist for collection {collection.name}")
                return []
                
        except PyMongoError as e:
            logger.error(f"Error creating indexes for collection {collection.name}: {e}")
            raise
    
    async def _validate_database_setup(self) -> Dict[str, Any]:
        """
        Validate that the database setup is correct.
        
        Returns:
            Dict[str, Any]: Validation results
        """
        try:
            database = db_service.get_database()
            validation_results = {
                "collections_exist": True,
                "indexes_exist": True,
                "missing_collections": [],
                "missing_indexes": {}
            }
            
            # Check collections exist
            existing_collections = set(database.list_collection_names())
            expected_collections = set(self.collections_config.keys())
            
            missing_collections = expected_collections - existing_collections
            if missing_collections:
                validation_results["collections_exist"] = False
                validation_results["missing_collections"] = list(missing_collections)
            
            # Check indexes exist
            for collection_name in expected_collections:
                if collection_name in existing_collections:
                    collection = database[collection_name]
                    existing_indexes = {idx.get("name") for idx in collection.list_indexes()}
                    expected_indexes = {idx.document.get("name") for idx in self.collections_config[collection_name]["indexes"]}
                    
                    missing_indexes = expected_indexes - existing_indexes
                    if missing_indexes:
                        validation_results["indexes_exist"] = False
                        validation_results["missing_indexes"][collection_name] = list(missing_indexes)
            
            return validation_results
            
        except Exception as e:
            logger.error(f"Database validation failed: {e}")
            return {
                "collections_exist": False,
                "indexes_exist": False,
                "error": str(e)
            }
    
    async def drop_all_indexes(self, collection_name: str) -> bool:
        """
        Drop all indexes for a collection (except _id).
        
        Args:
            collection_name: Name of the collection
            
        Returns:
            bool: True if successful
        """
        try:
            database = db_service.get_database()
            collection = database[collection_name]
            
            # Get all indexes except _id_
            indexes = list(collection.list_indexes())
            for index in indexes:
                index_name = index.get("name")
                if index_name != "_id_":
                    collection.drop_index(index_name)
                    logger.info(f"Dropped index {index_name} from collection {collection_name}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error dropping indexes for collection {collection_name}: {e}")
            return False
    
    async def recreate_indexes(self, collection_name: str) -> bool:
        """
        Recreate all indexes for a collection.
        
        Args:
            collection_name: Name of the collection
            
        Returns:
            bool: True if successful
        """
        try:
            if collection_name not in self.collections_config:
                raise ValueError(f"Unknown collection: {collection_name}")
            
            # Drop existing indexes
            await self.drop_all_indexes(collection_name)
            
            # Create new indexes
            database = db_service.get_database()
            collection = database[collection_name]
            config = self.collections_config[collection_name]
            
            await self._create_indexes(collection, config["indexes"])
            
            logger.info(f"Recreated indexes for collection {collection_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error recreating indexes for collection {collection_name}: {e}")
            return False


# Global database initializer instance
db_initializer = DatabaseInitializer()
