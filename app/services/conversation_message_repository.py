"""
ConversationMessage repository for MongoDB operations.

This module provides CRUD operations for ConversationMessage entities in MongoDB Atlas.
"""

import logging
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
from pymongo.collection import Collection
from pymongo.errors import Duplicate<PERSON>eyError, PyMongoError
from pymongo import DESCENDING
from bson import ObjectId

from app.utils.models.conversation_message import ConversationMessage
from app.services.db_service import db_service

logger = logging.getLogger(__name__)


class ConversationMessageRepository:
    """
    Repository class for ConversationMessage CRUD operations in MongoDB.
    
    Provides methods for creating, reading, updating, and deleting
    ConversationMessage entities with proper error handling and logging.
    """
    
    def __init__(self):
        self.collection_name = "conversation_messages"
    
    def _get_collection(self) -> Collection:
        """Get the conversation_messages collection from the database."""
        return db_service.get_collection(self.collection_name)
    
    async def create_message(self, message: ConversationMessage) -> ConversationMessage:
        """
        Create a new conversation message in the database.
        
        Args:
            message: ConversationMessage instance to create
            
        Returns:
            ConversationMessage: The created message with assigned ID
            
        Raises:
            DuplicateKeyError: If message with same telegram_message_id and chat_id already exists
            PyMongoError: For other database errors
        """
        try:
            collection = self._get_collection()
            
            # Convert to dict for MongoDB storage
            message_data = message.to_dict()
            
            # Remove _id if it exists (let MongoDB generate it)
            if "_id" in message_data:
                del message_data["_id"]
            
            # Insert the message
            result = collection.insert_one(message_data)
            
            # Update the message object with the new ID
            message.id = result.inserted_id
            
            logger.info(f"Created new conversation message: {message.telegram_message_id} in chat {message.chat_id}")
            return message
            
        except DuplicateKeyError as e:
            logger.error(f"Message with telegram_message_id {message.telegram_message_id} in chat {message.chat_id} already exists")
            raise
        except PyMongoError as e:
            logger.error(f"Database error creating message: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error creating message: {e}")
            raise
    
    async def get_message_by_telegram_id(self, telegram_message_id: int, chat_id: int) -> Optional[ConversationMessage]:
        """
        Get a conversation message by its Telegram message ID and chat ID.
        
        Args:
            telegram_message_id: The Telegram message ID
            chat_id: The chat ID
            
        Returns:
            ConversationMessage or None if not found
        """
        try:
            collection = self._get_collection()
            
            message_data = collection.find_one({
                "telegram_message_id": telegram_message_id,
                "chat_id": chat_id
            })
            
            if message_data:
                return ConversationMessage.from_dict(message_data)
            
            return None
            
        except PyMongoError as e:
            logger.error(f"Database error getting message by telegram_id {telegram_message_id}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error getting message by telegram_id {telegram_message_id}: {e}")
            raise
    
    async def get_message_by_id(self, message_id: ObjectId) -> Optional[ConversationMessage]:
        """
        Get a conversation message by its MongoDB ObjectId.
        
        Args:
            message_id: The MongoDB ObjectId
            
        Returns:
            ConversationMessage or None if not found
        """
        try:
            collection = self._get_collection()
            
            message_data = collection.find_one({"_id": message_id})
            
            if message_data:
                return ConversationMessage.from_dict(message_data)
            
            return None
            
        except PyMongoError as e:
            logger.error(f"Database error getting message by id {message_id}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error getting message by id {message_id}: {e}")
            raise
    
    async def update_message(self, message: ConversationMessage) -> ConversationMessage:
        """
        Update an existing conversation message in the database.
        
        Args:
            message: ConversationMessage instance with updated data
            
        Returns:
            ConversationMessage: The updated message
            
        Raises:
            ValueError: If message has no ID
            PyMongoError: For database errors
        """
        try:
            if not message.id:
                raise ValueError("Message must have an ID to be updated")
            
            collection = self._get_collection()
            
            # Convert to dict and remove _id for update
            message_data = message.to_dict()
            if "_id" in message_data:
                del message_data["_id"]
            
            # Update the message
            result = collection.update_one(
                {"_id": message.id},
                {"$set": message_data}
            )
            
            if result.matched_count == 0:
                raise ValueError(f"Message with ID {message.id} not found")
            
            logger.info(f"Updated conversation message: {message.telegram_message_id}")
            return message
            
        except PyMongoError as e:
            logger.error(f"Database error updating message: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error updating message: {e}")
            raise
    
    async def get_conversation_history(self, chat_id: int, telegram_user_id: int, 
                                     limit: int = 50, offset: int = 0) -> List[ConversationMessage]:
        """
        Get conversation history for a specific user in a chat.
        
        Args:
            chat_id: The chat ID
            telegram_user_id: The Telegram user ID
            limit: Maximum number of messages to return
            offset: Number of messages to skip
            
        Returns:
            List[ConversationMessage]: List of messages in reverse chronological order
        """
        try:
            collection = self._get_collection()
            
            cursor = collection.find({
                "chat_id": chat_id,
                "telegram_user_id": telegram_user_id
            }).sort("telegram_timestamp", DESCENDING).skip(offset).limit(limit)
            
            messages = []
            for message_data in cursor:
                messages.append(ConversationMessage.from_dict(message_data))
            
            return messages
            
        except PyMongoError as e:
            logger.error(f"Database error getting conversation history: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error getting conversation history: {e}")
            raise
    
    async def get_recent_messages(self, chat_id: int, hours: int = 24, limit: int = 100) -> List[ConversationMessage]:
        """
        Get recent messages from a chat within the specified time window.
        
        Args:
            chat_id: The chat ID
            hours: Number of hours to look back
            limit: Maximum number of messages to return
            
        Returns:
            List[ConversationMessage]: List of recent messages
        """
        try:
            collection = self._get_collection()
            
            # Calculate the cutoff time
            cutoff_time = datetime.utcnow() - timedelta(hours=hours)
            
            cursor = collection.find({
                "chat_id": chat_id,
                "telegram_timestamp": {"$gte": cutoff_time}
            }).sort("telegram_timestamp", DESCENDING).limit(limit)
            
            messages = []
            for message_data in cursor:
                messages.append(ConversationMessage.from_dict(message_data))
            
            return messages
            
        except PyMongoError as e:
            logger.error(f"Database error getting recent messages: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error getting recent messages: {e}")
            raise
    
    async def get_unprocessed_messages(self, limit: int = 50) -> List[ConversationMessage]:
        """
        Get messages that haven't been processed yet.
        
        Args:
            limit: Maximum number of messages to return
            
        Returns:
            List[ConversationMessage]: List of unprocessed messages
        """
        try:
            collection = self._get_collection()
            
            cursor = collection.find({
                "processing_status": {"$in": ["received", "processing"]}
            }).sort("received_at", 1).limit(limit)  # Oldest first
            
            messages = []
            for message_data in cursor:
                messages.append(ConversationMessage.from_dict(message_data))
            
            return messages
            
        except PyMongoError as e:
            logger.error(f"Database error getting unprocessed messages: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error getting unprocessed messages: {e}")
            raise
    
    async def mark_message_as_processed(self, message_id: ObjectId, bot_response: str,
                                      llm_provider: Optional[str] = None,
                                      tokens_used: Optional[int] = None,
                                      tools_executed: Optional[List[str]] = None,
                                      processing_time: Optional[float] = None) -> bool:
        """
        Mark a message as processed with response information.
        
        Args:
            message_id: The MongoDB ObjectId of the message
            bot_response: The bot's response text
            llm_provider: The LLM provider used
            tokens_used: Number of tokens used
            tools_executed: List of tools executed
            processing_time: Time taken to process
            
        Returns:
            bool: True if message was updated, False if not found
        """
        try:
            collection = self._get_collection()
            
            update_data = {
                "processing_status": "processed",
                "bot_response": bot_response,
                "bot_response_timestamp": datetime.utcnow()
            }
            
            if llm_provider:
                update_data["llm_provider_used"] = llm_provider
            if tokens_used:
                update_data["tokens_used"] = tokens_used
            if tools_executed:
                update_data["tools_executed"] = tools_executed
            if processing_time:
                update_data["response_processing_time"] = processing_time
            
            result = collection.update_one(
                {"_id": message_id},
                {"$set": update_data}
            )
            
            return result.matched_count > 0
            
        except PyMongoError as e:
            logger.error(f"Database error marking message as processed: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error marking message as processed: {e}")
            raise
    
    async def delete_old_messages(self, days: int = 30) -> int:
        """
        Delete messages older than the specified number of days.
        
        Args:
            days: Number of days to keep messages
            
        Returns:
            int: Number of messages deleted
        """
        try:
            collection = self._get_collection()
            
            # Calculate the cutoff time
            cutoff_time = datetime.utcnow() - timedelta(days=days)
            
            result = collection.delete_many({
                "telegram_timestamp": {"$lt": cutoff_time}
            })
            
            logger.info(f"Deleted {result.deleted_count} old messages")
            return result.deleted_count
            
        except PyMongoError as e:
            logger.error(f"Database error deleting old messages: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error deleting old messages: {e}")
            raise
