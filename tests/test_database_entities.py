"""
Tests for BotUser and ConversationMessage entities and repositories.

This module contains unit tests for the database entities and repository classes.
"""

import pytest
from datetime import datetime
from bson import ObjectId

from app.utils.models import BotUser, ConversationMessage


class TestBotUser:
    """Test cases for BotUser entity."""
    
    def test_bot_user_creation(self):
        """Test creating a BotUser instance."""
        user = BotUser(
            telegram_user_id=123456789,
            username="test_user",
            first_name="Test",
            last_name="User",
            language_code="en"
        )
        
        assert user.telegram_user_id == 123456789
        assert user.username == "test_user"
        assert user.first_name == "Test"
        assert user.last_name == "User"
        assert user.language_code == "en"
        assert user.is_active is True
        assert user.is_bot is False
        assert user.total_messages == 0
        assert isinstance(user.created_at, datetime)
        assert isinstance(user.updated_at, datetime)
    
    def test_bot_user_display_name(self):
        """Test getting display name for different user configurations."""
        # User with first and last name
        user1 = <PERSON><PERSON><PERSON><PERSON>(
            telegram_user_id=123,
            first_name="<PERSON>",
            last_name="<PERSON><PERSON>"
        )
        assert user1.get_display_name() == "<PERSON>e"
        
        # User with only first name
        user2 = BotUser(
            telegram_user_id=124,
            first_name="Jane"
        )
        assert user2.get_display_name() == "Jane"
        
        # User with only username
        user3 = BotUser(
            telegram_user_id=125,
            username="test_user"
        )
        assert user3.get_display_name() == "@test_user"
        
        # User with only telegram_user_id
        user4 = BotUser(telegram_user_id=126)
        assert user4.get_display_name() == "User 126"
    
    def test_bot_user_update_methods(self):
        """Test user update methods."""
        user = BotUser(telegram_user_id=123)
        original_updated_at = user.updated_at
        
        # Test update_last_seen
        user.update_last_seen()
        assert user.last_seen_at is not None
        assert user.updated_at > original_updated_at
        
        # Test add_chat
        user.add_chat(456)
        assert 456 in user.active_chats
        
        # Test adding same chat again (should not duplicate)
        user.add_chat(456)
        assert user.active_chats.count(456) == 1
        
        # Test increment_message_count
        original_count = user.total_messages
        user.increment_message_count()
        assert user.total_messages == original_count + 1
    
    def test_bot_user_serialization(self):
        """Test user serialization to/from dict."""
        user = BotUser(
            telegram_user_id=123,
            username="test_user",
            first_name="Test"
        )
        
        # Test to_dict
        user_dict = user.to_dict()
        assert isinstance(user_dict, dict)
        assert user_dict["telegram_user_id"] == 123
        assert user_dict["username"] == "test_user"
        
        # Test from_dict
        reconstructed_user = BotUser.from_dict(user_dict)
        assert reconstructed_user.telegram_user_id == user.telegram_user_id
        assert reconstructed_user.username == user.username
        assert reconstructed_user.first_name == user.first_name


class TestConversationMessage:
    """Test cases for ConversationMessage entity."""
    
    def test_conversation_message_creation(self):
        """Test creating a ConversationMessage instance."""
        message = ConversationMessage(
            telegram_message_id=1001,
            chat_id=-1001234567890,
            telegram_user_id=123456789,
            message_text="Hello, world!",
            telegram_timestamp=datetime.now()
        )
        
        assert message.telegram_message_id == 1001
        assert message.chat_id == -1001234567890
        assert message.telegram_user_id == 123456789
        assert message.message_text == "Hello, world!"
        assert message.message_type == "text"
        assert message.processing_status == "received"
        assert message.is_reply is False
        assert isinstance(message.received_at, datetime)
    
    def test_conversation_message_from_telegram(self):
        """Test creating ConversationMessage from Telegram message data."""
        telegram_message = {
            "message_id": 1001,
            "from": {
                "id": 123456789,
                "username": "test_user",
                "first_name": "Test",
                "last_name": "User"
            },
            "text": "Hello from Telegram!",
            "date": int(datetime.now().timestamp()),
            "chat": {
                "id": -1001234567890,
                "type": "private"
            }
        }
        
        message = ConversationMessage.from_telegram_message(
            telegram_message, 
            chat_id=-1001234567890
        )
        
        assert message.telegram_message_id == 1001
        assert message.telegram_user_id == 123456789
        assert message.username == "test_user"
        assert message.first_name == "Test"
        assert message.last_name == "User"
        assert message.message_text == "Hello from Telegram!"
        assert message.chat_type == "private"
    
    def test_conversation_message_status_updates(self):
        """Test message status update methods."""
        message = ConversationMessage(
            telegram_message_id=1001,
            chat_id=-1001234567890,
            telegram_user_id=123456789,
            message_text="Test message",
            telegram_timestamp=datetime.now()
        )
        
        # Test mark_as_processing
        message.mark_as_processing()
        assert message.processing_status == "processing"
        assert message.processed_at is not None
        
        # Test mark_as_processed
        message.mark_as_processed(
            bot_response="Test response",
            llm_provider="gemini",
            tokens_used=25,
            tools_executed=["calendar_check"],
            processing_time=1.5
        )
        assert message.processing_status == "processed"
        assert message.bot_response == "Test response"
        assert message.llm_provider_used == "gemini"
        assert message.tokens_used == 25
        assert message.tools_executed == ["calendar_check"]
        assert message.response_processing_time == 1.5
        assert message.bot_response_timestamp is not None
        
        # Test mark_as_error
        message.mark_as_error("Test error")
        assert message.processing_status == "error"
        assert message.error_message == "Test error"
        assert message.retry_count == 1
    
    def test_conversation_message_reply_context(self):
        """Test setting reply context."""
        message = ConversationMessage(
            telegram_message_id=1001,
            chat_id=-1001234567890,
            telegram_user_id=123456789,
            message_text="Reply message",
            telegram_timestamp=datetime.now()
        )
        
        message.set_reply_context(1000, "thread_123")
        assert message.is_reply is True
        assert message.reply_to_message_id == 1000
        assert message.thread_id == "thread_123"
    
    def test_conversation_message_keys(self):
        """Test conversation key generation."""
        message = ConversationMessage(
            telegram_message_id=1001,
            chat_id=-1001234567890,
            telegram_user_id=123456789,
            message_text="Test message",
            telegram_timestamp=datetime.now()
        )
        
        conversation_key = message.get_conversation_key()
        assert conversation_key == "-1001234567890_123456789"
    
    def test_conversation_message_sender_display_name(self):
        """Test getting sender display name."""
        # Message with full name
        message1 = ConversationMessage(
            telegram_message_id=1001,
            chat_id=-1001234567890,
            telegram_user_id=123456789,
            message_text="Test",
            telegram_timestamp=datetime.now(),
            first_name="John",
            last_name="Doe"
        )
        assert message1.get_sender_display_name() == "John Doe"
        
        # Message with only first name
        message2 = ConversationMessage(
            telegram_message_id=1002,
            chat_id=-1001234567890,
            telegram_user_id=123456789,
            message_text="Test",
            telegram_timestamp=datetime.now(),
            first_name="Jane"
        )
        assert message2.get_sender_display_name() == "Jane"
        
        # Message with only username
        message3 = ConversationMessage(
            telegram_message_id=1003,
            chat_id=-1001234567890,
            telegram_user_id=123456789,
            message_text="Test",
            telegram_timestamp=datetime.now(),
            username="test_user"
        )
        assert message3.get_sender_display_name() == "@test_user"
    
    def test_conversation_message_serialization(self):
        """Test message serialization to/from dict."""
        message = ConversationMessage(
            telegram_message_id=1001,
            chat_id=-1001234567890,
            telegram_user_id=123456789,
            message_text="Test message",
            telegram_timestamp=datetime.now()
        )
        
        # Test to_dict
        message_dict = message.to_dict()
        assert isinstance(message_dict, dict)
        assert message_dict["telegram_message_id"] == 1001
        assert message_dict["message_text"] == "Test message"
        
        # Test from_dict
        reconstructed_message = ConversationMessage.from_dict(message_dict)
        assert reconstructed_message.telegram_message_id == message.telegram_message_id
        assert reconstructed_message.message_text == message.message_text
        assert reconstructed_message.chat_id == message.chat_id
